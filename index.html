<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dots and Boxes Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #000;
            position: absolute;
            transform: translate(-50%, -50%);
        }
        
        .line {
            position: absolute;
            background-color: #ccc;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .line:hover {
            background-color: #999;
        }
        
        .line.drawn {
            background-color: #000;
            cursor: default;
        }
        
        .line.drawn:hover {
            background-color: #000;
        }
        
        .horizontal-line {
            height: 3px;
            width: 60px;
            transform: translateY(-50%);
        }
        
        .vertical-line {
            width: 3px;
            height: 60px;
            transform: translateX(-50%);
        }
        
        .box {
            position: absolute;
            width: 60px;
            height: 60px;
            border: none;
            transition: background-color 0.3s;
        }
        
        .box.player1 {
            background-color: rgba(239, 68, 68, 0.7);
        }
        
        .box.player2 {
            background-color: rgba(59, 130, 246, 0.7);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-4">
    <div class="flex flex-col lg:flex-row gap-8 items-start">
        <!-- Game Board -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-3xl font-bold text-center mb-6 text-gray-800">Dots and Boxes</h1>
            <div id="gameBoard" class="relative bg-gray-50 border-2 border-gray-200 rounded-lg" style="width: 300px; height: 300px;">
                <!-- Game elements will be generated here -->
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="bg-white rounded-lg shadow-lg p-6 min-w-[250px]">
            <h2 class="text-xl font-bold mb-4 text-gray-800">Game Info</h2>
            
            <!-- Current Turn -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-700">Current Turn</h3>
                <div id="currentTurn" class="flex items-center gap-2">
                    <span class="text-2xl">→</span>
                    <span class="font-bold text-red-500">Player 1</span>
                </div>
            </div>
            
            <!-- Scoreboard -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-700">Score</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-red-500 font-semibold">Player 1:</span>
                        <span id="player1Score" class="text-xl font-bold">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-500 font-semibold">Player 2:</span>
                        <span id="player2Score" class="text-xl font-bold">0</span>
                    </div>
                </div>
            </div>
            
            <!-- Turn Count -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-700">Turn Count</h3>
                <div id="turnCount" class="text-xl font-bold text-gray-600">1</div>
            </div>
            
            <!-- Game Status -->
            <div id="gameStatus" class="text-center p-3 rounded-lg bg-gray-100">
                <span class="text-gray-600">Game in progress...</span>
            </div>
            
            <!-- Reset Button -->
            <button id="resetBtn" class="w-full mt-4 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition-colors">
                New Game
            </button>
        </div>
    </div>

    <script>
        class DotsAndBoxesGame {
            constructor() {
                this.gridSize = 5;
                this.currentPlayer = 1;
                this.scores = { 1: 0, 2: 0 };
                this.turnCount = 1;
                this.gameBoard = document.getElementById('gameBoard');
                this.lines = new Set();
                this.boxes = [];
                this.gameEnded = false;
                
                this.initializeGame();
                this.setupEventListeners();
            }
            
            initializeGame() {
                this.gameBoard.innerHTML = '';
                this.lines.clear();
                this.boxes = [];
                this.currentPlayer = 1;
                this.scores = { 1: 0, 2: 0 };
                this.turnCount = 1;
                this.gameEnded = false;
                
                this.createDots();
                this.createLines();
                this.createBoxes();
                this.updateUI();
            }
            
            createDots() {
                const spacing = 60;
                const offset = 30;
                
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const dot = document.createElement('div');
                        dot.className = 'dot';
                        dot.style.left = `${offset + col * spacing}px`;
                        dot.style.top = `${offset + row * spacing}px`;
                        this.gameBoard.appendChild(dot);
                    }
                }
            }
            
            createLines() {
                const spacing = 60;
                const offset = 30;
                
                // Horizontal lines
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize - 1; col++) {
                        const line = document.createElement('div');
                        line.className = 'line horizontal-line';
                        line.style.left = `${offset + col * spacing + 6}px`;
                        line.style.top = `${offset + row * spacing}px`;
                        line.dataset.type = 'horizontal';
                        line.dataset.row = row;
                        line.dataset.col = col;
                        line.addEventListener('click', () => this.drawLine(line));
                        this.gameBoard.appendChild(line);
                    }
                }
                
                // Vertical lines
                for (let row = 0; row < this.gridSize - 1; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const line = document.createElement('div');
                        line.className = 'line vertical-line';
                        line.style.left = `${offset + col * spacing}px`;
                        line.style.top = `${offset + row * spacing + 6}px`;
                        line.dataset.type = 'vertical';
                        line.dataset.row = row;
                        line.dataset.col = col;
                        line.addEventListener('click', () => this.drawLine(line));
                        this.gameBoard.appendChild(line);
                    }
                }
            }
            
            createBoxes() {
                const spacing = 60;
                const offset = 30;

                for (let row = 0; row < this.gridSize - 1; row++) {
                    for (let col = 0; col < this.gridSize - 1; col++) {
                        const box = document.createElement('div');
                        box.className = 'box';
                        box.style.left = `${offset + col * spacing + 6}px`;
                        box.style.top = `${offset + row * spacing + 6}px`;
                        box.dataset.row = row;
                        box.dataset.col = col;
                        this.gameBoard.appendChild(box);
                        this.boxes.push({ row, col, owner: null, element: box });
                    }
                }
            }

            drawLine(lineElement) {
                if (lineElement.classList.contains('drawn') || this.gameEnded) {
                    return;
                }

                lineElement.classList.add('drawn');
                const lineId = `${lineElement.dataset.type}-${lineElement.dataset.row}-${lineElement.dataset.col}`;
                this.lines.add(lineId);

                const completedBoxes = this.checkCompletedBoxes();

                if (completedBoxes.length > 0) {
                    // Player gets another turn for completing boxes
                    this.scores[this.currentPlayer] += completedBoxes.length;
                    completedBoxes.forEach(box => {
                        box.owner = this.currentPlayer;
                        box.element.classList.add(`player${this.currentPlayer}`);
                    });
                } else {
                    // Switch turns
                    this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
                    this.turnCount++;
                }

                this.updateUI();
                this.checkGameEnd();
            }

            checkCompletedBoxes() {
                const newlyCompleted = [];

                this.boxes.forEach(box => {
                    if (box.owner === null && this.isBoxComplete(box.row, box.col)) {
                        newlyCompleted.push(box);
                    }
                });

                return newlyCompleted;
            }

            isBoxComplete(row, col) {
                const top = `horizontal-${row}-${col}`;
                const bottom = `horizontal-${row + 1}-${col}`;
                const left = `vertical-${row}-${col}`;
                const right = `vertical-${row}-${col + 1}`;

                return this.lines.has(top) && this.lines.has(bottom) &&
                       this.lines.has(left) && this.lines.has(right);
            }

            updateUI() {
                // Update current turn
                const currentTurnElement = document.getElementById('currentTurn');
                currentTurnElement.innerHTML = `
                    <span class="text-2xl">→</span>
                    <span class="font-bold ${this.currentPlayer === 1 ? 'text-red-500' : 'text-blue-500'}">
                        Player ${this.currentPlayer}
                    </span>
                `;

                // Update scores
                document.getElementById('player1Score').textContent = this.scores[1];
                document.getElementById('player2Score').textContent = this.scores[2];

                // Update turn count
                document.getElementById('turnCount').textContent = this.turnCount;
            }

            checkGameEnd() {
                const totalBoxes = (this.gridSize - 1) * (this.gridSize - 1);
                const completedBoxes = this.scores[1] + this.scores[2];

                if (completedBoxes === totalBoxes) {
                    this.gameEnded = true;
                    const winner = this.scores[1] > this.scores[2] ? 1 :
                                  this.scores[2] > this.scores[1] ? 2 : null;

                    const gameStatus = document.getElementById('gameStatus');
                    if (winner) {
                        gameStatus.innerHTML = `
                            <span class="font-bold ${winner === 1 ? 'text-red-500' : 'text-blue-500'}">
                                Player ${winner} Wins!
                            </span>
                        `;
                        gameStatus.className = 'text-center p-3 rounded-lg bg-green-100';
                    } else {
                        gameStatus.innerHTML = '<span class="font-bold text-gray-600">It\'s a Tie!</span>';
                        gameStatus.className = 'text-center p-3 rounded-lg bg-yellow-100';
                    }
                }
            }

            setupEventListeners() {
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.initializeGame();
                    document.getElementById('gameStatus').innerHTML = '<span class="text-gray-600">Game in progress...</span>';
                    document.getElementById('gameStatus').className = 'text-center p-3 rounded-lg bg-gray-100';
                });
            }
        }

        // Initialize the game when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DotsAndBoxesGame();
        });
    </script>
</body>
</html>
